// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {BaseCollection} from "../common/BaseCollection.sol";
import {ERC1155} from "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import {IERC2981} from "@openzeppelin/contracts/interfaces/IERC2981.sol";
import {CollectionParams} from "src/utils/Constants.sol";
import {Minted, BatchMinted} from "src/contracts/events/CollectionEvents.sol";
import "src/contracts/errors/CollectionErrors.sol";

contract ERC1155Collection is ERC1155, BaseCollection {
    // Event

    constructor(CollectionParams memory params) ERC1155(params.tokenURI) BaseCollection(params) {}

    // Mint a single NFT
    function mint(address to, uint256 amount) external payable {
        updateMintStage();
        uint256 requiredPayment = checkMint(to, amount);
        if (msg.value < requiredPayment) {
            revert Collection__InsufficientPayment();
        }

        uint256 tokenId = s_tokenIdCounter + 1;
        s_tokenIdCounter = tokenId;
        s_totalMinted += amount;
        s_mintedPerWallet[to] += amount;

        _mint(to, tokenId, amount, "");

        emit Minted(to, tokenId, amount);
    }

    // Batch mint NFTs
    function batchMintERC1155(address to, uint256 amount) external payable {
        updateMintStage();
        uint256 requiredPayment = checkMint(to, amount);
        if (msg.value < requiredPayment) {
            revert Collection__InsufficientPayment();
        }

        _batchMint(to, amount);
        emit BatchMinted(to, amount);
    }

    function _batchMint(address to, uint256 amount) internal {
        uint256 startTokenId = s_tokenIdCounter;
        uint256[] memory ids = new uint256[](amount);
        uint256[] memory values = new uint256[](amount);

        // Fill arrays
        for (uint256 i = 0; i < amount; i++) {
            ids[i] = startTokenId + i + 1;
            values[i] = 1;
        }

        // Update state
        s_tokenIdCounter = startTokenId + amount;
        s_totalMinted += amount;
        s_mintedPerWallet[to] += amount;

        // Perform batch mint
        _mintBatch(to, ids, values, "");
    }

    // Override uri to resolve conflict between ERC1155 and ERC1155URIStorage
    function uri(uint256 tokenId) public view override(ERC1155) returns (string memory) {
        return super.uri(tokenId);
    }

    function supportsInterface(bytes4 interfaceId) public view override(ERC1155) returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}
