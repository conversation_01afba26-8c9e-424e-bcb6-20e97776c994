// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {ERC721Collection} from "./ERC721Collection.sol";
import {ERC1155Collection} from "./ERC1155Collection.sol";
import {CollectionParams} from "src/utils/Constants.sol";
import {ERC721CollectionCreated, ERC1155CollectionCreated} from "src/contracts/events/CollectionEvents.sol";

contract CollectionFactory {
    function createERC721Collection(CollectionParams memory params) external returns (address) {
        ERC721Collection collection = new ERC721Collection(params);
        address collectionAddr = address(collection);
        emit ERC721CollectionCreated(collectionAddr, msg.sender);
        return collectionAddr;
    }

    function createERC1155Collection(CollectionParams memory params) external returns (address) {
        ERC1155Collection collection = new ERC1155Collection(params);
        address collectionAddr = address(collection);
        emit ERC1155CollectionCreated(collectionAddr, msg.sender);
        return collectionAddr;
    }
}
