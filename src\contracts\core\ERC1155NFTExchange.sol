// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {BaseNFTExchange} from "src/contracts/common/BaseNFTExchange.sol";
import {IERC1155} from "@openzeppelin/contracts/token/ERC1155/IERC1155.sol";
import "src/contracts/errors/NFTExchangeErrors.sol";
import "src/contracts/events/NFTExchangeEvents.sol";

// ERC-1155 NFT Exchange Contract
contract ERC1155NFTExchange is BaseNFTExchange {
    constructor(address m_marketplaceWallet) BaseNFTExchange(m_marketplaceWallet) {}

    // Function to list a single ERC-1155 NFT
    function listNFT(
        address m_contractAddress,
        uint256 m_tokenId,
        uint256 m_amount,
        uint256 m_price,
        uint256 m_listingDuration
    ) public {
        IERC1155 m_nftContract = IERC1155(m_contractAddress);
        if (m_nftContract.balanceOf(msg.sender, m_tokenId) < m_amount) revert NFTExchange__InsufficientBalance();
        if (!m_nftContract.isApprovedForAll(msg.sender, address(this))) revert NFTExchange__MarketplaceNotApproved();
        if (m_amount == 0) revert NFTExchange__AmountMustBeGreaterThanZero();
        if (m_price == 0) revert NFTExchange__PriceMustBeGreaterThanZero();
        if (m_listingDuration == 0) revert NFTExchange__DurationMustBeGreaterThanZero();

        bytes32 m_listingId = _generateListingId(m_contractAddress, m_tokenId, msg.sender);
        _createListing(m_contractAddress, m_tokenId, m_price, m_listingDuration, m_amount, m_listingId);
    }

    // Function to batch list ERC-1155 NFTs (same collection)
    function batchListNFT(
        address m_contractAddress,
        uint256[] memory m_tokenIds,
        uint256[] memory m_amounts,
        uint256[] memory m_prices,
        uint256 m_listingDuration
    ) public {
        if (m_tokenIds.length != m_amounts.length || m_tokenIds.length != m_prices.length) {
            revert NFTExchange__ArrayLengthMismatch();
        }
        if (m_listingDuration == 0) revert NFTExchange__DurationMustBeGreaterThanZero();

        IERC1155 m_nftContract = IERC1155(m_contractAddress);
        if (!m_nftContract.isApprovedForAll(msg.sender, address(this))) revert NFTExchange__MarketplaceNotApproved();

        for (uint256 i = 0; i < m_tokenIds.length; i++) {
            if (m_nftContract.balanceOf(msg.sender, m_tokenIds[i]) < m_amounts[i]) {
                revert NFTExchange__InsufficientBalance();
            }
            if (m_amounts[i] == 0) revert NFTExchange__AmountMustBeGreaterThanZero();
            if (m_prices[i] == 0) revert NFTExchange__PriceMustBeGreaterThanZero();

            bytes32 m_listingId = _generateListingId(m_contractAddress, m_tokenIds[i], msg.sender);
            _createListing(m_contractAddress, m_tokenIds[i], m_prices[i], m_listingDuration, m_amounts[i], m_listingId);
        }
    }

    // Function to buy an ERC-1155 NFT
    function buyNFT(bytes32 m_listingId) public payable onlyActiveListing(m_listingId) {
        Listing storage s_listing = s_listings[m_listingId];
        (address m_royaltyReceiver, uint256 m_royalty) =
            getRoyaltyInfo(s_listing.contractAddress, s_listing.tokenId, s_listing.price);
        uint256 m_takerFee = (s_listing.price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
        uint256 m_realityPrice = s_listing.price + m_royalty + m_takerFee;

        if (msg.value < m_realityPrice) revert NFTExchange__InsufficientPayment();

        IERC1155 m_nftContract = IERC1155(s_listing.contractAddress);
        m_nftContract.safeTransferFrom(s_listing.seller, msg.sender, s_listing.tokenId, s_listing.amount, "");

        _distributePayments(s_listing.seller, m_royaltyReceiver, s_listing.price, m_royalty, m_takerFee, m_realityPrice);
        _finalizeListing(m_listingId, s_listing.contractAddress, s_listing.seller);
    }

    // Function to batch buy ERC-1155 NFTs (same collection)
    function batchBuyNFT(bytes32[] memory m_listingIds) public payable {
        if (m_listingIds.length == 0) revert NFTExchange__ArrayLengthMismatch();

        Listing storage s_firstListing = s_listings[m_listingIds[0]];
        address m_contractAddress = s_firstListing.contractAddress;
        uint256 m_totalRealityPrice = 0;

        // First pass: Calculate total price and validate listings
        uint256 m_length = m_listingIds.length;
        for (uint256 i = 0; i < m_length; i++) {
            Listing storage s_listing = s_listings[m_listingIds[i]];
            if (s_listing.status != ListingStatus.Active) revert NFTExchange__NFTNotActive();
            if (block.timestamp >= s_listing.listingStart + s_listing.listingDuration) {
                revert NFTExchange__ListingExpired();
            }
            if (s_listing.contractAddress != m_contractAddress) revert NFTExchange__ArrayLengthMismatch();

            (address m_royaltyReceiver, uint256 m_royalty) =
                getRoyaltyInfo(s_listing.contractAddress, s_listing.tokenId, s_listing.price);
            uint256 m_takerFee = (s_listing.price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
            uint256 m_realityPrice = s_listing.price + m_royalty + m_takerFee;
            m_totalRealityPrice += m_realityPrice;
        }

        // Check payment before any transfers
        if (msg.value < m_totalRealityPrice) revert NFTExchange__InsufficientPayment();

        // Second pass: Execute transfers and distribute payments
        for (uint256 i = 0; i < m_length; i++) {
            Listing storage s_listing = s_listings[m_listingIds[i]];
            (address m_royaltyReceiver, uint256 m_royalty) =
                getRoyaltyInfo(s_listing.contractAddress, s_listing.tokenId, s_listing.price);
            uint256 m_takerFee = (s_listing.price * TAKER_FEE_BPS) / BPS_DENOMINATOR;
            uint256 m_realityPrice = s_listing.price + m_royalty + m_takerFee;

            // Transfer NFT first
            IERC1155 m_nftContract = IERC1155(s_listing.contractAddress);
            m_nftContract.safeTransferFrom(s_listing.seller, msg.sender, s_listing.tokenId, s_listing.amount, "");

            // Distribute payments
            if (m_royalty > 0 && m_royaltyReceiver != address(0)) {
                (bool royaltySuccess,) = payable(m_royaltyReceiver).call{value: m_royalty}("");
                if (!royaltySuccess) revert NFTExchange__TransferToCreatorFailed();
            }

            if (m_takerFee > 0) {
                (bool feeSuccess,) = payable(marketplaceWallet).call{value: m_takerFee}("");
                if (!feeSuccess) revert NFTExchange__TransferToMarketplaceFailed();
            }

            (bool sellerSuccess,) = payable(s_listing.seller).call{value: s_listing.price}("");
            if (!sellerSuccess) revert NFTExchange__TransferToSellerFailed();

            _finalizeListing(m_listingIds[i], s_listing.contractAddress, s_listing.seller);
        }
    }

    // Function to cancel listing
    function cancelListing(bytes32 m_listingId) public onlyActiveListing(m_listingId) {
        Listing storage s_listing = s_listings[m_listingId];
        if (s_listing.seller != msg.sender) revert NFTExchange__NotTheOwner();
        s_listings[m_listingId].status = ListingStatus.Cancelled;
        _removeListingFromArray(s_listingsByCollection[s_listing.contractAddress], m_listingId);
        _removeListingFromArray(s_listingsBySeller[s_listing.seller], m_listingId);
        emit NFTCancelled(m_listingId, s_listing.contractAddress, s_listing.tokenId);
    }

    // Function to batch cancel listings
    function batchCancelListing(bytes32[] memory m_listingIds) public {
        uint256 m_length = m_listingIds.length;
        for (uint256 i = 0; i < m_length; i++) {
            Listing storage s_listing = s_listings[m_listingIds[i]];
            if (s_listing.status != ListingStatus.Active) revert NFTExchange__NFTNotActive();
            if (block.timestamp >= s_listing.listingStart + s_listing.listingDuration) {
                revert NFTExchange__ListingExpired();
            }
            if (s_listing.seller != msg.sender) revert NFTExchange__NotTheOwner();
            s_listings[m_listingIds[i]].status = ListingStatus.Cancelled;
            _removeListingFromArray(s_listingsByCollection[s_listing.contractAddress], m_listingIds[i]);
            _removeListingFromArray(s_listingsBySeller[s_listing.seller], m_listingIds[i]);
            emit NFTCancelled(m_listingIds[i], s_listing.contractAddress, s_listing.tokenId);
        }
    }
}
