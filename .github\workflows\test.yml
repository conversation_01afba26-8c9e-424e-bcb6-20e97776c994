name: CI

on:
  push:
  pull_request:
  workflow_dispatch:

env:
  FOUNDRY_PROFILE: ci

jobs:
  check:
    name: Foundry project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Show Forge version
        run: |
          forge --version

      - name: Run Forge fmt with fallback
        id: fmt
        run: |
          forge fmt --check || (echo "Formatting code with forge fmt..." && forge fmt)


      # - name: Run Forge build
      #   run: |
      #     forge build --sizes
      #   id: build

      - name: Run Forge tests
        run: |
          forge test -vvv
        id: test
