// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import "forge-std/Script.sol";
import "src/contracts/core/NFTExchange.sol";

contract DeployNFTExchange is Script {
    address public marketplaceWallet=makeAddr("marketplaceWallet");
    function run() external {
        vm.startBroadcast();

        // Deploy ERC721 NFT Exchange
        NFTExchange exchange = new NFTExchange(marketplaceWallet);

        vm.stopBroadcast();

        // Save contract addresses to a JSON file
        string memory addresses = vm.serializeAddress(
            "addresses",
            "exchange",
            address(exchange)
        );


        // Write to file
        vm.writeFile("deployments/addresses.json", addresses);
        console2.log("Contract addresses saved to deployments/addresses.json");
    }
}
