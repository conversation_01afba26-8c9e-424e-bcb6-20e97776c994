= Access Control

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/access

This directory provides ways to restrict who can access the functions of a contract or when they can do it.

- {AccessManager} is a full-fledged access control solution for smart contract systems. Allows creating and assigning multiple hierarchical roles with execution delays for each account across various contracts.
- {AccessManaged} delegates its access control to an authority that dictates the permissions of the managed contract. It's compatible with an AccessManager as an authority.
- {AccessControl} provides a per-contract role based access control mechanism. Multiple hierarchical roles can be created and assigned each to multiple accounts within the same instance.
- {Ownable} is a simpler mechanism with a single owner "role" that can be assigned to a single account. This simpler mechanism can be useful for quick tests but projects with production concerns are likely to outgrow it.

== Core

{{Ownable}}

{{Ownable2Step}}

{{IAccessControl}}

{{AccessControl}}

== Extensions

{{IAccessControlEnumerable}}

{{AccessControlEnumerable}}

{{IAccessControlDefaultAdminRules}}

{{AccessControlDefaultAdminRules}}

== AccessManager

{{IAuthority}}

{{IAccessManager}}

{{AccessManager}}

{{IAccessManaged}}

{{AccessManaged}}

{{AuthorityUtils}}
