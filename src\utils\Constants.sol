// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

uint256 constant MAX_ROYALTY_FEE = 10000;

struct CollectionParams {
    string name;
    string symbol;
    address owner;
    string description;
    uint256 mintPrice;
    uint256 royaltyFee;
    uint256 maxSupply;
    uint256 mintLimitPerWallet;
    uint256 mintStartTime;
    uint256 allowlistMintPrice;
    uint256 publicMintPrice;
    uint256 allowlistStageDuration;
    string tokenURI;
}

enum MintStage {
    INACTIVE,
    ALLOWLIST,
    PUBLIC
}
