// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

// Base NFT Exchange Errors
error NFTExchange__InvalidMarketplaceWallet();
error NFTExchange__NFTNotActive();
error NFTExchange__ListingExpired();
error NFTExchange__InsufficientPayment();
error NFTExchange__TransferToSellerFailed();
error NFTExchange__TransferToCreatorFailed();
error NFTExchange__TransferToMarketplaceFailed();
error NFTExchange__RefundFailed();
error NFTExchange__InvalidTakerFee();

// ERC721 NFT Exchange Errors
error NFTExchange__NotTheOwner();
error NFTExchange__MarketplaceNotApproved();
error NFTExchange__PriceMustBeGreaterThanZero();
error NFTExchange__DurationMustBeGreaterThanZero();
error NFTExchange__ArrayLengthMismatch();

// ERC1155 NFT Exchange Errors
error NFTExchange__InsufficientBalance();
error NFTExchange__AmountMustBeGreaterThanZero();

// Factory Errors
error NFTExchange__ExchangeAlreadyExists();
error NFTExchange__InvalidExchangeType();
error NFTExchange__ExchangeDoesNotExist();
