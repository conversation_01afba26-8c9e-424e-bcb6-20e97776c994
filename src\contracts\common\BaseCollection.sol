// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {Fee} from "src/contracts/common/Fee.sol";
import {CollectionParams, MintStage} from "src/utils/Constants.sol";
import "src/contracts/errors/CollectionErrors.sol";

import {StageUpdated} from "src/contracts/events/CollectionEvents.sol";

contract BaseCollection is Ownable {
    string public s_description;
    uint256 public s_mintPrice; // Fallback mint price (wei)
    uint256 public s_maxSupply; // Maximum NFT supply
    uint256 public s_mintLimitPerWallet; // Mint limit per wallet
    uint256 public s_mintStartTime; // Mint start time
    uint256 public s_totalMinted; // Total minted
    Fee public s_feeContract; // Reference to Fee contract
    string public s_tokenURI;
    mapping(address => uint256) public s_mintedPerWallet;

    MintStage public s_currentStage;
    uint256 public s_allowlistMintPrice; // Mint price for allowlist
    uint256 public s_publicMintPrice; // Mint price for public
    uint256 public s_allowlistStageEnd; // End time for allowlist stage
    mapping(address => bool) public s_allowlist; // Allowlist
    uint256 public s_tokenIdCounter;

    constructor(CollectionParams memory params) Ownable(params.owner) {
        s_description = params.description;
        s_mintPrice = params.mintPrice;
        s_maxSupply = params.maxSupply;
        s_mintLimitPerWallet = params.mintLimitPerWallet;
        s_mintStartTime = params.mintStartTime;
        s_allowlistMintPrice = params.allowlistMintPrice;
        s_publicMintPrice = params.publicMintPrice;
        s_allowlistStageEnd = params.mintStartTime + params.allowlistStageDuration;
        s_currentStage = MintStage.INACTIVE;
        s_tokenURI = params.tokenURI;
        s_feeContract = new Fee(params.owner, params.royaltyFee);
    }

    // Add addresses to allowlist
    function addToAllowlist(address[] calldata addresses) external onlyOwner {
        for (uint256 i = 0; i < addresses.length; i++) {
            s_allowlist[addresses[i]] = true;
        }
    }

    // Update mint stage
    function updateMintStage() public {
        if (block.timestamp < s_mintStartTime) {
            s_currentStage = MintStage.INACTIVE;
        } else if (block.timestamp < s_allowlistStageEnd) {
            s_currentStage = MintStage.ALLOWLIST;
        } else {
            s_currentStage = MintStage.PUBLIC;
        }
        emit StageUpdated(s_currentStage, block.timestamp);
    }

    // Check mint conditions
    function checkMint(address to, uint256 amount) internal view returns (uint256 requiredPayment) {
        _validateMintConditions(to, amount);
        return _calculateRequiredPayment(amount);
    }

    // Validate mint conditions
    function _validateMintConditions(address to, uint256 amount) internal view {
        if (block.timestamp < s_mintStartTime) {
            revert Collection__MintingNotActive();
        }
        if (s_currentStage == MintStage.INACTIVE) {
            revert Collection__MintingNotStarted();
        }
        if (s_mintedPerWallet[to] + amount > s_mintLimitPerWallet) {
            revert Collection__MintLimitExceeded();
        }
        if (s_totalMinted + amount > s_maxSupply) {
            revert Collection__MintLimitExceeded();
        }

        if (s_currentStage == MintStage.ALLOWLIST) {
            if (!s_allowlist[to]) revert Collection__NotInAllowlist();
        }
    }

    // Calculate required payment
    function _calculateRequiredPayment(uint256 amount) internal view returns (uint256) {
        if (amount == 0) revert Collection__InvalidAmount();
        return s_currentStage == MintStage.ALLOWLIST ? s_allowlistMintPrice * amount : s_publicMintPrice * amount;
    }

    // Getter functions
    function getDescription() external view returns (string memory) {
        return s_description;
    }

    function getMintPrice() external view returns (uint256) {
        return s_mintPrice;
    }

    function getMaxSupply() external view returns (uint256) {
        return s_maxSupply;
    }

    function getMintLimitPerWallet() external view returns (uint256) {
        return s_mintLimitPerWallet;
    }

    function getMintStartTime() external view returns (uint256) {
        return s_mintStartTime;
    }

    function getTotalMinted() external view returns (uint256) {
        return s_totalMinted;
    }

    function getCurrentStage() external view returns (MintStage) {
        return s_currentStage;
    }

    function getAllowlistMintPrice() external view returns (uint256) {
        return s_allowlistMintPrice;
    }

    function getPublicMintPrice() external view returns (uint256) {
        return s_publicMintPrice;
    }

    function getAllowlistStageEnd() external view returns (uint256) {
        return s_allowlistStageEnd;
    }

    function isInAllowlist(address account) external view returns (bool) {
        return s_allowlist[account];
    }

    function getMintedPerWallet(address account) external view returns (uint256) {
        return s_mintedPerWallet[account];
    }

    function getFeeContract() external view returns (Fee) {
        return s_feeContract;
    }
}
