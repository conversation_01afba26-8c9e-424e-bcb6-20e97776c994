// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Test} from "forge-std/Test.sol";
import {CollectionFactory} from "src/contracts/core/Collection.sol";
import {CollectionParams} from "src/utils/Constants.sol";
import {DeployCollection} from "script/DeployCollection.s.sol";
import {ERC721CollectionCreated, ERC1155CollectionCreated} from "src/contracts/events/CollectionEvents.sol";

contract UnitCollectionFactoryTest is Test {
    struct TestSetup {
        CollectionFactory factory;
        CollectionParams params;
        address owner;
    }

    TestSetup private setup;

    function setUp() public {
        setup.owner = makeAddr("owner");
        DeployCollection deployCollection = new DeployCollection();
        setup.factory = deployCollection.run();

        setup.params = CollectionParams({
            owner: setup.owner,
            name: "Test Collection",
            symbol: "TEST",
            description: "Test Description",
            tokenURI: "ipfs://test",
            mintPrice: 0.1 ether,
            maxSupply: 1000,
            mintLimitPerWallet: 5,
            mintStartTime: block.timestamp + 1 days,
            allowlistMintPrice: 0.08 ether,
            publicMintPrice: 0.1 ether,
            allowlistStageDuration: 1 days,
            royaltyFee: 250 // 2.5%
        });
    }

    function test_CreateERC721Collection() public {
        vm.startPrank(setup.owner);
        address collection = setup.factory.createERC721Collection(setup.params);
        vm.stopPrank();

        assertTrue(collection != address(0), "Collection address should not be zero");
    }

    function test_CreateERC1155Collection() public {
        vm.startPrank(setup.owner);
        address collection = setup.factory.createERC1155Collection(setup.params);
        vm.stopPrank();

        assertTrue(collection != address(0), "Collection address should not be zero");
    }

    function test_CreateMultipleCollections() public {
        vm.startPrank(setup.owner);

        // Create ERC721 collections
        address collection1 = setup.factory.createERC721Collection(setup.params);
        address collection2 = setup.factory.createERC721Collection(setup.params);

        // Create ERC1155 collections
        address collection3 = setup.factory.createERC1155Collection(setup.params);
        address collection4 = setup.factory.createERC1155Collection(setup.params);

        vm.stopPrank();

        // Verify all collections were created with unique addresses
        assertTrue(collection1 != address(0), "Collection 1 should not be zero");
        assertTrue(collection2 != address(0), "Collection 2 should not be zero");
        assertTrue(collection3 != address(0), "Collection 3 should not be zero");
        assertTrue(collection4 != address(0), "Collection 4 should not be zero");
        assertTrue(collection1 != collection2, "Collections should be unique");
        assertTrue(collection3 != collection4, "Collections should be unique");
    }

    function test_Events() public {
        vm.startPrank(setup.owner);

        // Test ERC721 collection creation event
        vm.expectEmit(true, true, true, true);
        address erc721Collection = setup.factory.createERC721Collection(setup.params);
        emit ERC721CollectionCreated(erc721Collection, setup.owner);

        // Test ERC1155 collection creation event
        vm.expectEmit(true, true, true, true);
        address erc1155Collection = setup.factory.createERC1155Collection(setup.params);
        emit ERC1155CollectionCreated(erc1155Collection, setup.owner);

        vm.stopPrank();
    }
}
