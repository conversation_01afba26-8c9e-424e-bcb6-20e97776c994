# Contributing Guidelines

There are many ways to contribute to OpenZeppelin Contracts.

## Troubleshooting

You can help other users in the community to solve their smart contract issues in the [OpenZeppelin Forum].

[OpenZeppelin Forum]: https://forum.openzeppelin.com/

## Opening an issue

You can [open an issue] to suggest a feature or report a minor bug. For serious bugs please do not open an issue, instead refer to our [security policy] for appropriate steps.

If you believe your issue may be due to user error and not a problem in the library, consider instead posting a question on the [OpenZeppelin Forum].

Before opening an issue, be sure to search through the existing open and closed issues, and consider posting a comment in one of those instead.

When requesting a new feature, include as many details as you can, especially around the use cases that motivate it. Features are prioritized according to the impact they may have on the ecosystem, so we appreciate information showing that the impact could be high.

[security policy]: https://github.com/OpenZeppelin/openzeppelin-contracts/security
[open an issue]: https://github.com/OpenZeppelin/openzeppelin-contracts/issues/new/choose

## Submitting a pull request

If you would like to contribute code or documentation you may do so by forking the repository and submitting a pull request.

Any non-trivial code contribution must be first discussed with the maintainers in an issue (see [Opening an issue](#opening-an-issue)). Only very minor changes are accepted without prior discussion.

Make sure to read and follow the [engineering guidelines](./GUIDELINES.md). Run linter and tests to make sure your pull request is good before submitting it.

Changelog entries should be added to each pull request by using [Changesets](https://github.com/changesets/changesets/).

When opening the pull request you will be presented with a template and a series of instructions. Read through it carefully and follow all the steps. Expect a review and feedback from the maintainers afterwards.

If you're looking for a good place to start, look for issues labelled ["good first issue"](https://github.com/OpenZeppelin/openzeppelin-contracts/labels/good%20first%20issue)!
