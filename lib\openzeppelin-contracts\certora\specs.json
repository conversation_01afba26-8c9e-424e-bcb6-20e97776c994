[{"spec": "Pausable", "contract": "PausableHarness", "files": ["certora/harnesses/PausableHarness.sol"]}, {"spec": "AccessControl", "contract": "AccessControlHarness", "files": ["certora/harnesses/AccessControlHarness.sol"]}, {"spec": "AccessControlDefaultAdminRules", "contract": "AccessControlDefaultAdminRulesHarness", "files": ["certora/harnesses/AccessControlDefaultAdminRulesHarness.sol"]}, {"spec": "AccessManager", "contract": "AccessManagerHarness", "files": ["certora/harnesses/AccessManagerHarness.sol"], "options": ["--optimistic_hashing", "--optimistic_loop"]}, {"spec": "AccessManaged", "contract": "AccessManagedHarness", "files": ["certora/harnesses/AccessManagedHarness.sol", "certora/harnesses/AccessManagerHarness.sol"], "options": ["--optimistic_hashing", "--optimistic_loop", "--link AccessManagedHarness:_authority=AccessManagerHarness"]}, {"spec": "DoubleEndedQueue", "contract": "DoubleEnded<PERSON><PERSON><PERSON><PERSON><PERSON>ness", "files": ["certora/harnesses/DoubleEndedQueueHarness.sol"]}, {"spec": "Ownable", "contract": "OwnableHarness", "files": ["certora/harnesses/OwnableHarness.sol"]}, {"spec": "Ownable2Step", "contract": "Ownable2StepHarness", "files": ["certora/harnesses/Ownable2StepHarness.sol"]}, {"spec": "ERC20", "contract": "ERC20PermitHarness", "files": ["certora/harnesses/ERC20PermitHarness.sol"], "options": ["--optimistic_loop"]}, {"spec": "ERC20FlashMint", "contract": "ERC20FlashMintHarness", "files": ["certora/harnesses/ERC20FlashMintHarness.sol", "certora/harnesses/ERC3156FlashBorrowerHarness.sol"], "options": ["--optimistic_loop"]}, {"spec": "ERC20Wrapper", "contract": "ERC20WrapperHarness", "files": ["certora/harnesses/ERC20PermitHarness.sol", "certora/harnesses/ERC20WrapperHarness.sol"], "options": ["--link ERC20WrapperHarness:_underlying=ERC20PermitHarness", "--optimistic_loop"]}, {"spec": "ERC721", "contract": "ERC721<PERSON><PERSON>ness", "files": ["certora/harnesses/ERC721Harness.sol", "certora/harnesses/ERC721ReceiverHarness.sol"], "options": ["--optimistic_loop"]}, {"spec": "Initializable", "contract": "InitializableHarness", "files": ["certora/harnesses/InitializableHarness.sol"]}, {"spec": "EnumerableSet", "contract": "EnumerableSetHarness", "files": ["certora/harnesses/EnumerableSetHarness.sol"]}, {"spec": "EnumerableMap", "contract": "EnumerableMapHarness", "files": ["certora/harnesses/EnumerableMapHarness.sol"]}, {"spec": "TimelockController", "contract": "TimelockControllerHarness", "files": ["certora/harnesses/TimelockControllerHarness.sol"], "options": ["--optimistic_hashing", "--optimistic_loop"]}, {"spec": "Nonces", "contract": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "files": ["certora/harnesses/NoncesHarness.sol"]}]