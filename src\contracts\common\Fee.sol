// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {MAX_ROYALTY_FEE} from "src/utils/Constants.sol";
import {FeeUpdated} from "src/contracts/events/FeeEvents.sol";

contract Fee is Ownable {
    uint256 public s_royaltyFee; // Royalty fee (in basis points, e.g., 500 = 5%)

    constructor(address owner_, uint256 royaltyFee_) Ownable(owner_) {
        require(royaltyFee_ <= MAX_ROYALTY_FEE, "Royalty fee exceeds 100%");
        s_royaltyFee = royaltyFee_;
    }

    // Setter for royaltyFee
    function setRoyaltyFee(uint256 newRoyaltyFee) external onlyOwner {
        require(newRoyaltyFee <= MAX_ROYALTY_FEE, "Royalty fee exceeds 100%");
        s_royaltyFee = newRoyaltyFee;
        emit FeeUpdated("royaltyFee", newRoyaltyFee);
    }

    // Getter for royaltyFee
    function getRoyaltyFee() external view returns (uint256) {
        return s_royaltyFee;
    }

    // Support royalty via EIP-2981
    function royaltyInfo(uint256, uint256 salePrice) external view returns (address receiver, uint256 royaltyAmount) {
        return (owner(), (salePrice * s_royaltyFee) / 10000);
    }
}
