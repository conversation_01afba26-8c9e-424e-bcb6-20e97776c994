// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Test, console2} from "forge-std/Test.sol";
import {NFTExchange} from "src/contracts/core/NFTExchange.sol";
import {ERC721NFTExchange} from "src/contracts/core/ERC721NFTExchange.sol";
import {ERC1155NFTExchange} from "src/contracts/core/ERC1155NFTExchange.sol";
import "src/contracts/errors/NFTExchangeErrors.sol";

contract NFTExchangeTest is Test {
    NFTExchange public exchange;
    address public owner;
    address public marketplace;
    address public user;

    event ExchangeCreated(address indexed exchangeAddress, NFTExchange.ExchangeType exchangeType);
    event ExchangeRemoved(address indexed exchangeAddress);
    event MarketplaceWalletUpdated(address indexed oldWallet, address indexed newWallet);

    function setUp() public {
        owner = makeAddr("owner");
        marketplace = makeAddr("marketplace");
        user = makeAddr("user");

        vm.prank(owner);
        exchange = new NFTExchange(marketplace);
    }

    // Test constructor
    function test_Constructor() public {
        assertEq(exchange.owner(), owner);
        assertEq(exchange.marketplaceWallet(), marketplace);
    }

    function test_Constructor_ZeroAddress() public {
        vm.expectRevert(NFTExchange__InvalidMarketplaceWallet.selector);
        new NFTExchange(address(0));
    }

    // Test createExchange
    function test_CreateExchange_ERC721() public {
        vm.prank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC721);

        assertTrue(exchange.isValidExchange(exchangeAddress));
        assertEq(uint256(exchange.getExchangeType(exchangeAddress)), uint256(NFTExchange.ExchangeType.ERC721));
    }

    function test_CreateExchange_ERC1155() public {
        vm.prank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC1155);

        assertTrue(exchange.isValidExchange(exchangeAddress));
        assertEq(uint256(exchange.getExchangeType(exchangeAddress)), uint256(NFTExchange.ExchangeType.ERC1155));
    }

    function test_CreateExchange_NotOwner() public {
        vm.prank(user);
        vm.expectRevert(abi.encodeWithSelector(bytes4(keccak256("OwnableUnauthorizedAccount(address)")), user));
        exchange.createExchange(NFTExchange.ExchangeType.ERC721);
    }

    function test_CreateExchange_AlreadyExists() public {
        vm.startPrank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC721);

        // Try to create another exchange of the same type and check for revert
        vm.expectRevert(NFTExchange__ExchangeAlreadyExists.selector);
        exchange.createExchange(NFTExchange.ExchangeType.ERC721);

        // Ensure the original exchange is still valid
        assertTrue(exchange.isValidExchange(exchangeAddress));
        vm.stopPrank();
    }

    // Test removeExchange
    function test_RemoveExchange() public {
        vm.startPrank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC721);
        exchange.removeExchange(exchangeAddress);
        vm.stopPrank();

        assertFalse(exchange.isValidExchange(exchangeAddress));
    }

    function test_RemoveExchange_NotOwner() public {
        vm.startPrank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC721);
        vm.stopPrank();

        vm.prank(user);
        vm.expectRevert(abi.encodeWithSelector(bytes4(keccak256("OwnableUnauthorizedAccount(address)")), user));
        exchange.removeExchange(exchangeAddress);
    }

    function test_RemoveExchange_DoesNotExist() public {
        vm.prank(owner);
        vm.expectRevert(NFTExchange__ExchangeDoesNotExist.selector);
        exchange.removeExchange(makeAddr("nonExistentExchange"));
    }

    // Test updateMarketplaceWallet
    function test_UpdateMarketplaceWallet() public {
        address newWallet = makeAddr("newWallet");

        vm.prank(owner);
        vm.expectEmit(true, true, false, false);
        emit MarketplaceWalletUpdated(marketplace, newWallet);
        exchange.updateMarketplaceWallet(newWallet);

        assertEq(exchange.marketplaceWallet(), newWallet);
    }

    function test_UpdateMarketplaceWallet_NotOwner() public {
        address newWallet = makeAddr("newWallet");

        vm.prank(user);
        vm.expectRevert(abi.encodeWithSelector(bytes4(keccak256("OwnableUnauthorizedAccount(address)")), user));
        exchange.updateMarketplaceWallet(newWallet);
    }

    function test_UpdateMarketplaceWallet_ZeroAddress() public {
        vm.prank(owner);
        vm.expectRevert(NFTExchange__InvalidMarketplaceWallet.selector);
        exchange.updateMarketplaceWallet(address(0));
    }

    // Test isValidExchange
    function test_IsValidExchange() public {
        vm.startPrank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC721);
        vm.stopPrank();

        assertTrue(exchange.isValidExchange(exchangeAddress));
        assertFalse(exchange.isValidExchange(makeAddr("nonExistentExchange")));
    }

    // Test getExchangeType
    function test_GetExchangeType() public {
        vm.startPrank(owner);
        address exchangeAddress = exchange.createExchange(NFTExchange.ExchangeType.ERC721);
        vm.stopPrank();

        assertEq(uint256(exchange.getExchangeType(exchangeAddress)), uint256(NFTExchange.ExchangeType.ERC721));
    }

    function test_GetExchangeType_DoesNotExist() public {
        vm.expectRevert(NFTExchange__ExchangeDoesNotExist.selector);
        exchange.getExchangeType(makeAddr("nonExistentExchange"));
    }

    // Test multiple exchanges
    function test_MultipleExchanges() public {
        vm.startPrank(owner);
        address erc721Exchange = exchange.createExchange(NFTExchange.ExchangeType.ERC721);
        address erc1155Exchange = exchange.createExchange(NFTExchange.ExchangeType.ERC1155);

        assertTrue(exchange.isValidExchange(erc721Exchange));
        assertTrue(exchange.isValidExchange(erc1155Exchange));
        assertEq(uint256(exchange.getExchangeType(erc721Exchange)), uint256(NFTExchange.ExchangeType.ERC721));
        assertEq(uint256(exchange.getExchangeType(erc1155Exchange)), uint256(NFTExchange.ExchangeType.ERC1155));

        exchange.removeExchange(erc721Exchange);
        vm.stopPrank();

        assertFalse(exchange.isValidExchange(erc721Exchange));
        assertTrue(exchange.isValidExchange(erc1155Exchange));
    }
}
