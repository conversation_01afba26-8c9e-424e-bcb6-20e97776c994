// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {ERC721} from "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import {IERC2981} from "@openzeppelin/contracts/interfaces/IERC2981.sol";
import {ERC721URIStorage} from "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import {Strings} from "@openzeppelin/contracts/utils/Strings.sol";
import {BaseCollection} from "src/contracts/common/BaseCollection.sol";
import {IERC165} from "@openzeppelin/contracts/interfaces/IERC165.sol";
import {CollectionParams} from "src/utils/Constants.sol";
import {Minted, BatchMinted} from "src/contracts/events/CollectionEvents.sol";
import "src/contracts/errors/CollectionErrors.sol";

contract ERC721Collection is ERC721, BaseCollection, IERC2981, ERC721URIStorage {
    constructor(CollectionParams memory params) ERC721(params.name, params.symbol) BaseCollection(params) {}

    function mint(address to) external payable {
        updateMintStage();
        uint256 requiredPayment = checkMint(to, 1);
        if (msg.value < requiredPayment) {
            revert Collection__InsufficientPayment();
        }
        s_tokenIdCounter++;
        _mintWithURI(to, s_tokenIdCounter);
    }

    function batchMintERC721(address to, uint256 amount) external payable {
        updateMintStage();
        uint256 requiredPayment = checkMint(to, amount);
        if (msg.value < requiredPayment) {
            revert Collection__InsufficientPayment();
        }
        _batchMint(to, amount);
    }

    function _batchMint(address to, uint256 amount) internal {
        uint256 startId = s_tokenIdCounter;
        s_tokenIdCounter += amount;

        for (uint256 i = 0; i < amount; i++) {
            _mintWithURI(to, startId + i + 1);
        }
        emit BatchMinted(to, amount);
    }

    function _mintWithURI(address to, uint256 tokenId) internal {
        s_mintedPerWallet[to]++;
        s_totalMinted++;
        _mint(to, tokenId);
        _setTokenURI(tokenId, _getTokenURI(tokenId));
        emit Minted(to, tokenId, 1);
    }

    function _getTokenURI(uint256 tokenId) internal view returns (string memory) {
        return string(abi.encodePacked(s_tokenURI, "/", Strings.toString(tokenId), ".json"));
    }

    function tokenURI(uint256 tokenId) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function royaltyInfo(uint256, uint256 salePrice)
        external
        view
        override
        returns (address receiver, uint256 royaltyAmount)
    {
        return (owner(), (salePrice * s_feeContract.getRoyaltyFee()) / 10000);
    }

    function supportsInterface(bytes4 interfaceId)
        public
        view
        override(ERC721, ERC721URIStorage, IERC165)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }
}
