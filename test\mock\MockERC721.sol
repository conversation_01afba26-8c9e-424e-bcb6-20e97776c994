// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import {Fee} from "src/contracts/common/Fee.sol";

contract MockERC721 is ERC721, Ownable {
    Fee public feeContract;

    constructor(string memory name, string memory symbol) ERC721(name, symbol) Ownable(msg.sender) {
        feeContract = new Fee(msg.sender, 0); // 0% royalty fee for testing
    }

    // Function to mint NFTs
    function mint(address to, uint256 tokenId) public onlyOwner {
        _mint(to, tokenId);
    }

    // Function to burn NFTs (optional, for testing cleanup)
    function burn(uint256 tokenId) public {
        require(ownerOf(tokenId) == msg.sender, "MockERC721: caller is not owner");
        _burn(tokenId);
    }

    // Function to get fee contract
    function getFeeContract() external view returns (Fee) {
        return feeContract;
    }
}
