// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {ERC721NFTExchange} from "./ERC721NFTExchange.sol";
import {ERC1155NFTExchange} from "./ERC1155NFTExchange.sol";
import "src/contracts/errors/NFTExchangeErrors.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title NFTExchange
 * @notice Factory contract for creating and managing NFT exchanges
 */
contract NFTExchange is Ownable {
    // Enum for exchange types
    enum ExchangeType {
        ERC721,
        ERC1155
    }

    // Mapping to store created exchanges
    mapping(address => ExchangeType) public exchanges;
    // Mapping to track if an address is a registered exchange
    mapping(address => bool) public isExchangeRegistered;
    // Mapping to track if an exchange type already exists
    mapping(uint256 => bool) public exchangeTypeExists;
    // Store addresses of exchanges by type for easy removal
    mapping(ExchangeType => address) public exchangeByType;

    // Events
    event ExchangeCreated(address indexed exchangeAddress, ExchangeType exchangeType);
    event ExchangeRemoved(address indexed exchangeAddress);
    event MarketplaceWalletUpdated(address indexed oldWallet, address indexed newWallet);

    // Marketplace wallet that can be updated by owner
    address public marketplaceWallet;

    constructor(address m_marketplaceWallet) Ownable(msg.sender) {
        if (m_marketplaceWallet == address(0)) revert NFTExchange__InvalidMarketplaceWallet();
        marketplaceWallet = m_marketplaceWallet;
    }

    /**
     * @notice Creates a new NFT exchange
     * @param m_exchangeType The type of exchange to create (ERC721 or ERC1155)
     * @return The address of the newly created exchange
     */
    function createExchange(ExchangeType m_exchangeType) external onlyOwner returns (address) {
        // Check if an exchange of this type already exists
        if (exchangeTypeExists[uint256(m_exchangeType)]) {
            revert NFTExchange__ExchangeAlreadyExists();
        }

        address m_exchangeAddress;
        if (m_exchangeType == ExchangeType.ERC721) {
            m_exchangeAddress = address(new ERC721NFTExchange(marketplaceWallet));
        } else if (m_exchangeType == ExchangeType.ERC1155) {
            m_exchangeAddress = address(new ERC1155NFTExchange(marketplaceWallet));
        } else {
            revert NFTExchange__InvalidExchangeType();
        }

        // Register the exchange
        isExchangeRegistered[m_exchangeAddress] = true;
        exchanges[m_exchangeAddress] = m_exchangeType;
        exchangeTypeExists[uint256(m_exchangeType)] = true;
        exchangeByType[m_exchangeType] = m_exchangeAddress;
        emit ExchangeCreated(m_exchangeAddress, m_exchangeType);

        return m_exchangeAddress;
    }

    /**
     * @notice Removes an exchange from the registry
     * @param m_exchangeAddress The address of the exchange to remove
     */
    function removeExchange(address m_exchangeAddress) external onlyOwner {
        if (!isExchangeRegistered[m_exchangeAddress]) {
            revert NFTExchange__ExchangeDoesNotExist();
        }

        ExchangeType exchangeType = exchanges[m_exchangeAddress];
        delete isExchangeRegistered[m_exchangeAddress];
        delete exchanges[m_exchangeAddress];
        delete exchangeTypeExists[uint256(exchangeType)];
        delete exchangeByType[exchangeType];
        emit ExchangeRemoved(m_exchangeAddress);
    }

    /**
     * @notice Updates the marketplace wallet address
     * @param m_newMarketplaceWallet The new marketplace wallet address
     */
    function updateMarketplaceWallet(address m_newMarketplaceWallet) external onlyOwner {
        if (m_newMarketplaceWallet == address(0)) revert NFTExchange__InvalidMarketplaceWallet();
        address oldWallet = marketplaceWallet;
        marketplaceWallet = m_newMarketplaceWallet;
        emit MarketplaceWalletUpdated(oldWallet, m_newMarketplaceWallet);
    }

    /**
     * @notice Checks if an address is a valid exchange
     * @param m_exchangeAddress The address to check
     * @return True if the address is a valid exchange, false otherwise
     */
    function isValidExchange(address m_exchangeAddress) external view returns (bool) {
        return isExchangeRegistered[m_exchangeAddress];
    }

    /**
     * @notice Gets the type of an exchange
     * @param m_exchangeAddress The address of the exchange
     * @return The type of the exchange
     */
    function getExchangeType(address m_exchangeAddress) external view returns (ExchangeType) {
        if (!isExchangeRegistered[m_exchangeAddress]) {
            revert NFTExchange__ExchangeDoesNotExist();
        }
        return exchanges[m_exchangeAddress];
    }
}
