// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import {Fee} from "src/contracts/common/Fee.sol";

contract MockERC1155 is ERC1155, Ownable {
    string private _name;
    string private _symbol;
    Fee public feeContract;

    constructor(string memory name_, string memory symbol_) ERC1155("") Ownable(msg.sender) {
        _name = name_;
        _symbol = symbol_;
        feeContract = new Fee(msg.sender, 0); // 0% royalty fee for testing
    }

    function name() public view returns (string memory) {
        return _name;
    }

    function symbol() public view returns (string memory) {
        return _symbol;
    }

    function mint(address to, uint256 id, uint256 amount) public {
        _mint(to, id, amount, "");
    }

    function mintBatch(address to, uint256[] memory ids, uint256[] memory amounts) public {
        _mintBatch(to, ids, amounts, "");
    }

    // Function to get fee contract
    function getFeeContract() external view returns (Fee) {
        return feeContract;
    }
}
