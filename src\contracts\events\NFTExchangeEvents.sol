// SPDX-License-Identifier: MIT

pragma solidity ^0.8.30;

event NFTListed(
    bytes32 indexed listingId, address indexed contractAddress, uint256 indexed tokenId, address seller, uint256 price
);

event NFTBought(
    bytes32 indexed listingId,
    address indexed contractAddress,
    uint256 indexed tokenId,
    address seller,
    address buyer,
    uint256 price
);

event NFTCancelled(bytes32 indexed listingId, address indexed contractAddress, uint256 indexed tokenId);

event MarketplaceWalletUpdated(address indexed oldWallet, address indexed newWallet);

event TakerFeeUpdated(uint256 oldFee, uint256 newFee);
