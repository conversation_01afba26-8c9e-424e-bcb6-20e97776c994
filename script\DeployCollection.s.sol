// SPDX-License-Identifier: MIT

pragma solidity ^0.8.30;

import {Script, console2} from "forge-std/Script.sol";
import {CollectionFactory} from "../src/contracts/core/Collection.sol";

contract DeployCollection is Script {
    function run() external returns (CollectionFactory) {
        vm.startBroadcast();
        CollectionFactory collectionFactory = new CollectionFactory();
        vm.stopBroadcast();
        console2.log(
            "Collection Factory deployed at:",
            address(collectionFactory)
        );

        // Save contract address to a JSON file
        string memory addresses = vm.serializeAddress(
            "addresses",
            "collectionFactory",
            address(collectionFactory)
        );

        // Write to file
        vm.writeFile("deployments/collection.json", addresses);
        console2.log(
            "Collection Factory address saved to deployments/collection.json"
        );
        return collectionFactory;
    }
}

