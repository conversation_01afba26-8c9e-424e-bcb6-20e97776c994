// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Test, console} from "forge-std/Test.sol";
import {Fee} from "src/contracts/common/Fee.sol";
import {MAX_ROYALTY_FEE} from "src/utils/Constants.sol";

contract UnitFeeTest is Test {
    struct TestSetup {
        Fee fee;
        address owner;
        address user;
    }

    TestSetup private setup;
    Fee private fee;
    address private owner;
    address private user;
    uint256 private constant INITIAL_ROYALTY_FEE = 500; // 5%

    function setUp() public {
        setup.owner = makeAddr("owner");
        setup.user = makeAddr("user");
        vm.startPrank(setup.owner);
        setup.fee = new Fee(setup.owner, INITIAL_ROYALTY_FEE);
        vm.stopPrank();
    }

    function test_Constructor_ValidFee() public {
        assertEq(setup.fee.s_royaltyFee(), INITIAL_ROYALTY_FEE);
        assertEq(setup.fee.owner(), setup.owner);
    }

    function test_Constructor_InvalidFee() public {
        vm.expectRevert("Royalty fee exceeds 100%");
        new Fee(setup.owner, MAX_ROYALTY_FEE + 1);
    }

    function test_SetRoyaltyFee_Valid() public {
        uint256 newFee = 750; // 7.5%
        vm.startPrank(setup.owner);
        setup.fee.setRoyaltyFee(newFee);
        assertEq(setup.fee.s_royaltyFee(), newFee);
        vm.stopPrank();
    }

    function test_SetRoyaltyFee_Invalid() public {
        vm.startPrank(setup.owner);
        vm.expectRevert("Royalty fee exceeds 100%");
        setup.fee.setRoyaltyFee(MAX_ROYALTY_FEE + 1);
        vm.stopPrank();
    }

    function test_SetRoyaltyFee_NotOwner() public {
        vm.startPrank(setup.user);
        vm.expectRevert(abi.encodeWithSignature("OwnableUnauthorizedAccount(address)", setup.user));
        setup.fee.setRoyaltyFee(750);
        vm.stopPrank();
    }

    function test_GetRoyaltyFee() public {
        assertEq(setup.fee.getRoyaltyFee(), INITIAL_ROYALTY_FEE);
    }

    function test_RoyaltyInfo_ZeroPrice() public {
        (address receiver, uint256 amount) = setup.fee.royaltyInfo(1, 0);
        assertEq(receiver, setup.owner);
        assertEq(amount, 0);
    }

    function test_RoyaltyInfo_NormalPrice() public {
        uint256 salePrice = 1 ether;
        (address receiver, uint256 amount) = setup.fee.royaltyInfo(1, salePrice);
        assertEq(receiver, setup.owner);
        assertEq(amount, (salePrice * INITIAL_ROYALTY_FEE) / 10000);
    }

    function test_RoyaltyInfo_MaxPrice() public {
        uint256 salePrice = 1000 ether; // Use a large but reasonable price
        (address receiver, uint256 amount) = setup.fee.royaltyInfo(1, salePrice);
        assertEq(receiver, setup.owner);
        assertEq(amount, (salePrice * INITIAL_ROYALTY_FEE) / 10000);
    }
}
